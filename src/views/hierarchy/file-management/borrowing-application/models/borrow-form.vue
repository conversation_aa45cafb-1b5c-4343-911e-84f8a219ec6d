<template>
    <alert-content :buttons="buttons" :on-default-save="handleSave">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
            :disabled="isPreview"
        >
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="申请人" :span="12">
                    <n-input v-model:value="$store.main.userInfo.nickname" readonly />
                </n-form-item-gi>

                <n-form-item-gi label="申请日期" :span="12">
                    <n-input :value="dayjs().format('YYYY-MM-DD')" readonly></n-input>
                </n-form-item-gi>

                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="formData.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi label="借阅原因" path="borrowReason" :span="24">
                    <n-select
                        v-model:value="formData.borrowReason"
                        value-field="label"
                        :options="borrowReasonOptions"
                        placeholder="请选择"
                        class="w-160px"
                    />
                    <n-input
                        v-if="formData.borrowReason === 'other'"
                        v-model:value="formData.otherReason"
                        placeholder="请输入其他原因"
                        class="ml-10px flex-1"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="documents">
                    <div class="flex-v w-100%">
                        <div class="flex justify-between">
                            <span class="ml-2px mb-10px required-field">借阅清单</span>
                            <n-button type="primary" size="small" @click="handleAddFile">添加文件</n-button>
                        </div>
                        <vxe-table
                            ref="tableRef"
                            class="w-100%"
                            :border="true"
                            show-overflow
                            auto-resize
                            :edit-rules="validRules"
                            :valid-config="{ showMessage: false }"
                            :edit-config="{
                                trigger: 'click',
                                mode: 'cell'
                            }"
                            :data="formData.documents"
                        >
                            <vxe-column type="seq" title="序号" width="70" fixed="left"></vxe-column>
                            <vxe-column
                                field="documentValidity"
                                title="文件有效性"
                                minWidth="140"
                                :edit-render="{ name: 'VxeSelect', options: documentValidityOptions }"
                            ></vxe-column>
                            <vxe-column
                                field="documentModuleType"
                                title="文件类型"
                                minWidth="120"
                                :edit-render="{
                                    name: 'VxeSelect',
                                    options: typeOptions,
                                    events: { change: handleDocumentModuleTypeChange }
                                }"
                            ></vxe-column>
                            <vxe-column
                                field="documentCategory"
                                title="文件类别"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="
                                    ({ row }) => formatOptionLabel(row.id, 'categoryOptions', row.documentCategory)
                                "
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentCategory"
                                        :options="getRowOptions(row.id, 'documentCategoryOptions')"
                                        @change="handleDocumentCategoryChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentName"
                                title="文件名称"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="
                                    ({ row }) => formatOptionLabel(row.id, 'documentNameOptions', row.documentName)
                                "
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentName"
                                        :options="getRowOptions(row.id, 'documentNameOptions')"
                                        @change="handleDocumentNameChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentNo"
                                title="文件编号"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'documentNoOptions', row.documentNo)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentNo"
                                        :options="getRowOptions(row.id, 'documentNoOptions')"
                                        @change="handleDocumentNoChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentVersionNo"
                                title="版本/版次"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="
                                    ({ row }) =>
                                        formatOptionLabel(row.id, 'documentVersionNoOptions', row.documentVersionNo)
                                "
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentVersionNo"
                                        :options="getRowOptions(row.id, 'documentVersionNoOptions')"
                                        @change="handleDocumentVersionNoChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column field="todo" title="操作" width="80" fixed="right">
                                <template v-slot="{ row }">
                                    <n-button size="tiny" type="error" @click="handleRemoveFile(row)">删除</n-button>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { NButton, NInput, NSelect, type FormRules } from 'naive-ui';
import { DocumentLibraryData, DocumentLibraryForm } from '@/api/apis/nebula/api/v1/document-library/loans';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';
import { VxeSelect } from 'vxe-pc-ui';
import { RowVO } from '@/api/sass/api/v1/dict';
import { ButtonsConfig } from '@/components/alert-content.vue';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';

const props = defineProps<{
    row?: DocumentLibraryData;
    isPreview?: boolean;
}>();

const buttons: ButtonsConfig = {
    save: {
        text: '提交'
    },
    extra: {
        saveTemp: {
            text: '暂存',
            type: 'success'
        }
    }
};

// 表单数据
const formData = ref<DocumentLibraryForm>({
    borrowPeriod: null,
    borrowReason: '',
    otherReason: '',
    documents: []
});

// 为每行单独管理选项数据
const rowOptionsMap = ref<
    Map<
        string,
        {
            documentCategoryOptions: Record<string, any>[];
            documentNameOptions: Record<string, any>[];
            documentNoOptions: Record<string, any>[];
            documentVersionNoOptions: Record<string, any>[];
        }
    >
>(new Map());

// 借阅原因选项
const borrowReasonOptions = [
    { label: '项目需要 / 研究', value: 1 },
    { label: '问题调查 / 分析', value: 2 },
    { label: '审计 / 检查准备', value: 3 },
    { label: '培训 / 学习需要', value: 4 },
    { label: '其他', value: 0 }
];

// 表单验证规则
const rules: FormRules = {
    borrowPeriod: { required: true, message: '请输入借阅日期', trigger: ['blur', 'change'], type: 'array' },
    borrowReason: { required: true, message: '请选择借阅原因', trigger: ['blur', 'change'] },
    documents: {
        required: true,
        validator: () => {
            if (!formData.value.documents?.length) {
                return new Error('请填写借阅清单（至少一个文件）');
            }
            return true;
        }
    }
};

// 表格验证
const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    documentValidity: [{ required: true, message: '请选择文件有效性' }],
    documentModuleType: [{ required: true, message: '请选择文件类型' }],
    documentCategory: [{ required: true, message: '请选择文件分类' }],
    documentId: [{ required: true, message: '请输入文件名称' }],
    documentNo: [{ required: true, message: '请输入文件编号' }],
    documentVersionNo: [{ required: true, message: '请输入文件版本' }]
});

// 添加文件
const handleAddFile = () => {
    const newID = nanoid();
    formData.value.documents?.push({
        id: newID,
        documentModuleType: '',
        documentCategory: null,
        documentId: '',
        documentNo: null,
        documentVersionNo: '',
        documentValidity: null
    });

    // 为新行初始化选项数据
    initRowOptions(newID);
};

// 删除文件
const handleRemoveFile = (row: any) => {
    const index = formData.value.documents?.findIndex((item) => item.id === row.id);
    if (index !== undefined && index >= 0) {
        formData.value.documents?.splice(index, 1);
        // 清理对应的选项数据
        rowOptionsMap.value.delete(row.id);
    }
};

// 初始化行选项数据
const initRowOptions = (rowId: string) => {
    rowOptionsMap.value.set(rowId, {
        documentCategoryOptions: [],
        documentNameOptions: [],
        documentNoOptions: [],
        documentVersionNoOptions: []
    });
};

// 获取指定行的选项数据
const getRowOptions = (rowId: string, optionType: string) => {
    const rowOptions = rowOptionsMap.value.get(rowId);
    if (!rowOptions) {
        initRowOptions(rowId);
        return [];
    }
    return rowOptions[optionType as keyof typeof rowOptions] || [];
};

// 格式化选项标签 - 将 id 转换为对应的 label 显示
const formatOptionLabel = (rowId: string, optionType: string, value: string) => {
    if (!value) return '';
    const options = getRowOptions(rowId, optionType);
    const option = options.find((opt) => opt.value === value);
    return option ? option.label : value;
};

// 状态选项
const documentValidityOptions = [
    { label: '有效', value: 'effective' },
    { label: '作废', value: 'cancel' }
];

// 类型选项
const typeOptions = [
    { label: '内部文件', value: 'internal' },
    { label: '外部文件', value: 'external' }
];
const handleDocumentModuleTypeChange = async (row: any, rowIndex: any) => {
    try {
        const dict = await api.sass.api.v1.dict.get('file_business_dictionary');
        let categoryId = '';
        switch (rowIndex.value) {
            case 'internal':
                categoryId = dict.data.data[0].extra.fileCategory.internal;
                break;
            case 'external':
                categoryId = dict.data.data[0].extra.fileCategory.external;
                break;
        }
        if (!categoryId) {
            window.$message.error('获取文件类别树字典数据失败');
            return;
        }
        const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: categoryId
        });

        // 更新指定行的类别选项
        const rowOptions = rowOptionsMap.value.get(row.row.id);
        if (rowOptions) {
            rowOptions.documentCategoryOptions = $utils.treeData.convertTreeData(res.data);
        }

        // 清空该行的文件类别选择
        const fileItem = formData.value.documents?.find((item) => item.id === row.row.id);
        if (fileItem) {
            fileItem.documentCategory = '';
            fileItem.documentId = '';
            fileItem.documentNo = '';
            fileItem.documentVersionNo = '';
        }
    } catch (error) {
        window.$message.error('获取文件类别失败');
    }
};

// 文件类别变更处理
const handleDocumentCategoryChange = async (event: any, row: any) => {
    const categoryValue = event?.value || event;
    console.log('🚀 ~ handleDocumentCategoryChange ~ categoryValue:', categoryValue);
    const rowId = row?.id || row;
    console.log('🚀 ~ handleDocumentCategoryChange ~ row:', row);

    try {
        // 根据选择的文件类别获取对应的文件名称选项
        if (row.documentModuleType === 'internal') {
            // 查询内部文件
            const res = await $apis.nebula.api.v1.internal.list({
                noPage: true,
                docCategoryIds: [categoryValue]
            })
        }
        // 这里需要根据实际API调整
        const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: categoryValue
        });

        // 更新指定行的文件名称选项
        const rowOptions = rowOptionsMap.value.get(rowId);
        if (rowOptions) {
            rowOptions.documentNameOptions = $utils.treeData.convertTreeData(res.data);
        }

        // 清空该行的后续选择
        const fileItem = formData.value.documents?.find((item) => item.id === rowId);
        if (fileItem) {
            fileItem.documentName = '';
            fileItem.documentNo = '';
            fileItem.documentVersionNo = '';
        }
    } catch (error) {
        window.$message.error('获取文件名称选项失败');
    }
};

// 文件名称变更处理
const handleDocumentNameChange = async (event: any, row: any) => {
    const documentNameValue = event?.value || event;
    const rowId = row?.id || row;

    try {
        // 根据选择的文件名称获取对应的文件编号选项
        // 这里使用模拟数据，实际项目中需要替换为真实API
        // const res = await $apis.nebula.api.v1.file.getdocumentNos({
        //     documentName: documentNameValue
        // });

        // 模拟数据
        const mockDocumentNos = [
            { label: `编号-${documentNameValue}-001`, value: `${documentNameValue}-001` },
            { label: `编号-${documentNameValue}-002`, value: `${documentNameValue}-002` },
            { label: `编号-${documentNameValue}-003`, value: `${documentNameValue}-003` }
        ];

        // 更新指定行的文件编号选项
        const rowOptions = rowOptionsMap.value.get(rowId);
        if (rowOptions) {
            rowOptions.documentNoOptions = mockDocumentNos;
        }

        // 清空该行的后续选择
        const fileItem = formData.value.documents?.find((item) => item.id === rowId);
        if (fileItem) {
            fileItem.documentNo = '';
            fileItem.documentVersionNo = '';
        }
    } catch (error) {
        console.error('获取文件编号选项失败:', error);
        window.$message.error('获取文件编号选项失败');
    }
};

// 文件编号变更处理
const handleDocumentNoChange = async (event: any, row: any) => {
    const documentNoValue = event?.value || event;
    const rowId = row?.id || row;

    try {
        // 根据选择的文件编号获取对应的版本选项
        // 这里使用模拟数据，实际项目中需要替换为真实API
        // const res = await $apis.nebula.api.v1.file.getFileVersions({
        //     documentNo: documentNoValue
        // });

        // 模拟数据
        const mockDocumentVersionNos = [
            { label: `版本-${documentNoValue}-v1`, value: `${documentNoValue}-v1` },
            { label: `版本-${documentNoValue}-v2`, value: `${documentNoValue}-v2` },
            { label: `版本-${documentNoValue}-v3`, value: `${documentNoValue}-v3` }
        ];

        // 更新指定行的版本选项
        const rowOptions = rowOptionsMap.value.get(rowId);
        if (rowOptions) {
            rowOptions.documentVersionNoOptions = mockDocumentVersionNos;
        }

        // 清空该行的版本选择
        const fileItem = formData.value.documents?.find((item) => item.id === rowId);
        if (fileItem) {
            fileItem.documentVersionNo = '';
        }
    } catch (error) {
        console.error('获取文件版本选项失败:', error);
        window.$message.error('获取文件版本选项失败');
    }
};

// 文件版本变更处理
const handleDocumentVersionNoChange = async (event: any, row: any) => {
    const documentVersionNoValue = event?.value || event;
    const rowId = row?.id || row;

    console.log('文件版本选择:', documentVersionNoValue, '行ID:', rowId);
    // 这里可以添加版本选择后的逻辑
};

// 保存表单
const formRef = ref();
const tableRef = ref();
const handleSave = async () => {
    try {
        await formRef.value?.validate();
        await $utils.vxeTableConfig.tableValid(tableRef);
    } catch (err: any) {
        // 用来提示表单必填错误
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }

    const _data = {
        borrowReason: formData.value.borrowReason,
        borrowTime: formData.value.borrowPeriod?.[0],
        dueTime: formData.value.borrowPeriod?.[1],
        documents: formData.value.documents.map((item) => {
            return {
                documentId: item.documentId,
                documentModuleType: item.documentModuleType,
                documentVersionNo: item.documentVersionNo
            };
        })
    };
    if (props.row?.id) {
        // 编辑
        console.log('编辑借阅申请:', formData.value);
        await $apis.nebula.api.v1.documentLibrary.loans.update(_data);
        window.$message.success('修改成功');
    } else {
        // 新增
        console.log('新增借阅申请:', formData.value);
        await $apis.nebula.api.v1.documentLibrary.loans.create(_data);
        window.$message.success('新增成功');
    }
};

// 获取借阅清单
const getDocuments = async (id: string) => {
    const { data } = await $apis.nebula.api.v1.documentLibrary.loans.documents(id);
    formData.value.documents = data;
};

// 初始化数据
onMounted(() => {
    if (props.row) {
        formData.value = { ...props.row } as any;
        if (props.row.id) getDocuments(props.row.id);
    }

    // 为现有的文件列表项初始化选项数据
    formData.value.documents?.forEach((item) => {
        if (item.id) {
            initRowOptions(item.id);
        }
    });
});
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
.placeholder-text {
    color: #c0c4cc;
    font-style: italic;
}
</style>
